{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "com.police.complaint.system", "asar": true, "productName": "Police Complaint System", "copyright": "© 2024 République du Tchad - Police Nationale", "directories": {"output": "build-output/${version}"}, "files": ["dist", "dist-electron", "!node_modules", "!src", "!electron", "!*.md", "!*.config.*"], "asarUnpack": ["dist-electron/sql-wasm.wasm"], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "artifactName": "Police-Complaint-System-Windows-${version}-Portable.${ext}", "icon": null, "sign": null, "certificateFile": null, "certificatePassword": null}, "publish": null, "nodeGypRebuild": false, "npmRebuild": false, "buildDependenciesFromSource": false, "forceCodeSigning": false}