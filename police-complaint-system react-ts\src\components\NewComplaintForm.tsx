import React, { useState, useEffect } from 'react'
import { Save, AlertCircle, CheckCircle, FileText } from 'lucide-react'
import { PDFGenerator } from '../services/pdfGenerator'

interface ComplaintType {
  id: number
  name_fr: string
  name_ar: string
  color_code: string
}

interface ComplaintFormData {
  complainant_name: string
  complainant_address: string
  complainant_phone: string
  complainant_id_number: string
  accused_name: string
  complaint_type_id: number
  description_fr: string
  description_ar: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes: string
  officer_notes: string
  officer_in_charge: string
}

const NewComplaintForm: React.FC = () => {
  const [complaintTypes, setComplaintTypes] = useState<ComplaintType[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  
  const [formData, setFormData] = useState<ComplaintFormData>({
    complainant_name: '',
    complainant_address: '',
    complainant_phone: '',
    complainant_id_number: '',
    accused_name: '',
    complaint_type_id: 0,
    description_fr: '',
    description_ar: '',
    location_incident: '',
    date_incident: new Date().toISOString().split('T')[0],
    status: 'pending',
    priority: 'medium',
    evidence_notes: '',
    officer_notes: '',
    officer_in_charge: 'Agent de Police'
  })

  const [errors, setErrors] = useState<Partial<ComplaintFormData>>({})

  useEffect(() => {
    loadComplaintTypes()
  }, [])

  const loadComplaintTypes = async () => {
    try {
      const types = await window.electronAPI.getComplaintTypes()
      setComplaintTypes(types)
    } catch (error) {
      console.error('Error loading complaint types:', error)
      setMessage({ type: 'error', text: 'Erreur lors du chargement des types de plaintes' })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<ComplaintFormData> = {}

    if (!formData.complainant_name.trim()) {
      newErrors.complainant_name = 'Le nom du plaignant est requis'
    }

    if (!formData.complaint_type_id) {
      newErrors.complaint_type_id = 'Le type de plainte est requis' as any
    }

    if (!formData.description_fr.trim() && !formData.description_ar.trim()) {
      newErrors.description_fr = 'Une description est requise (français ou arabe)'
    }

    if (!formData.location_incident.trim()) {
      newErrors.location_incident = 'Le lieu de l\'incident est requis'
    }

    if (!formData.date_incident) {
      newErrors.date_incident = 'La date de l\'incident est requise'
    }

    if (!formData.officer_in_charge.trim()) {
      newErrors.officer_in_charge = 'L\'officier responsable est requis'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      setMessage({ type: 'error', text: 'Veuillez corriger les erreurs dans le formulaire' })
      return
    }

    setIsLoading(true)
    setMessage(null)

    try {
      // Step 1: Save the complaint to database
      const complaint = await window.electronAPI.createComplaint({
        ...formData,
        complaint_number: '' // Will be generated by the backend
      })

      // Step 2: Get the complaint type information for PDF
      const complaintTypes = await window.electronAPI.getComplaintTypes()
      const complaintType = complaintTypes.find(type => type.id === complaint.complaint_type_id)

      // Step 3: Create complaint object with type info for PDF
      const complaintWithTypeInfo = {
        ...complaint,
        type_name_fr: complaintType?.name_fr,
        type_name_ar: complaintType?.name_ar,
        color_code: complaintType?.color_code
      }

      // Step 4: Generate PDF report
      const pdfGenerator = PDFGenerator.getInstance()
      await pdfGenerator.generateComplaintReport(complaintWithTypeInfo)

      setMessage({
        type: 'success',
        text: `Plainte créée avec succès. Numéro: ${complaint.complaint_number}. Le rapport PDF a été généré et téléchargé.`
      })

      // Reset form
      setFormData({
        complainant_name: '',
        complainant_address: '',
        complainant_phone: '',
        complainant_id_number: '',
        accused_name: '',
        complaint_type_id: 0,
        description_fr: '',
        description_ar: '',
        location_incident: '',
        date_incident: new Date().toISOString().split('T')[0],
        status: 'pending',
        priority: 'medium',
        evidence_notes: '',
        officer_notes: '',
        officer_in_charge: 'Agent de Police'
      })
      setErrors({})
    } catch (error) {
      console.error('Error creating complaint or generating PDF:', error)
      setMessage({ type: 'error', text: 'Erreur lors de la création de la plainte ou de la génération du PDF' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: keyof ComplaintFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="card">
        <div className="border-b border-gray-200 pb-4 mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Nouvelle Plainte</h2>
          <p className="text-sm text-gray-600 mt-1">شكوى جديدة</p>
        </div>

        {message && (
          <div className={`mb-6 p-4 rounded-md flex items-center ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 mr-2" />
            ) : (
              <AlertCircle className="w-5 h-5 mr-2" />
            )}
            {message.text}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Complainant Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Informations du Plaignant / معلومات المشتكي
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">
                  Nom Complet / الاسم الكامل *
                </label>
                <input
                  type="text"
                  className={`form-input ${errors.complainant_name ? 'border-red-500' : ''}`}
                  value={formData.complainant_name}
                  onChange={(e) => handleInputChange('complainant_name', e.target.value)}
                  placeholder="Nom et prénom du plaignant"
                />
                {errors.complainant_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.complainant_name}</p>
                )}
              </div>

              <div>
                <label className="form-label">
                  Numéro d'Identité / رقم الهوية
                </label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.complainant_id_number}
                  onChange={(e) => handleInputChange('complainant_id_number', e.target.value)}
                  placeholder="Numéro de carte d'identité"
                />
              </div>

              <div>
                <label className="form-label">
                  Adresse / العنوان
                </label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.complainant_address}
                  onChange={(e) => handleInputChange('complainant_address', e.target.value)}
                  placeholder="Adresse complète"
                />
              </div>

              <div>
                <label className="form-label">
                  Téléphone / الهاتف
                </label>
                <input
                  type="tel"
                  className="form-input"
                  value={formData.complainant_phone}
                  onChange={(e) => handleInputChange('complainant_phone', e.target.value)}
                  placeholder="+235 XX XX XX XX"
                />
              </div>
            </div>
          </div>

          {/* Accused Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Informations sur le Mis en Cause / معلومات المشتكى عليه
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">
                  Nom du Mis en Cause / اسم المشتكى عليه
                </label>
                <input
                  type="text"
                  className="form-input"
                  value={formData.accused_name}
                  onChange={(e) => handleInputChange('accused_name', e.target.value)}
                  placeholder="Nom de la personne accusée (si connu)"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optionnel - Laissez vide si inconnu
                </p>
              </div>
            </div>
          </div>

          {/* Complaint Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Détails de la Plainte / تفاصيل الشكوى
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">
                  Type de Plainte / نوع الشكوى *
                </label>
                <select
                  className={`form-input ${errors.complaint_type_id ? 'border-red-500' : ''}`}
                  value={formData.complaint_type_id}
                  onChange={(e) => handleInputChange('complaint_type_id', parseInt(e.target.value))}
                >
                  <option value={0}>Sélectionner un type</option>
                  {complaintTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name_fr} / {type.name_ar}
                    </option>
                  ))}
                </select>
                {errors.complaint_type_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.complaint_type_id}</p>
                )}
              </div>

              <div>
                <label className="form-label">
                  Priorité / الأولوية
                </label>
                <select
                  className="form-input"
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                >
                  <option value="low">Faible / منخفض</option>
                  <option value="medium">Moyen / متوسط</option>
                  <option value="high">Élevé / عالي</option>
                  <option value="urgent">Urgent / عاجل</option>
                </select>
              </div>

              <div>
                <label className="form-label">
                  Lieu de l'Incident / مكان الحادث *
                </label>
                <input
                  type="text"
                  className={`form-input ${errors.location_incident ? 'border-red-500' : ''}`}
                  value={formData.location_incident}
                  onChange={(e) => handleInputChange('location_incident', e.target.value)}
                  placeholder="Lieu exact où s'est produit l'incident"
                />
                {errors.location_incident && (
                  <p className="text-red-500 text-sm mt-1">{errors.location_incident}</p>
                )}
              </div>

              <div>
                <label className="form-label">
                  Date de l'Incident / تاريخ الحادث *
                </label>
                <input
                  type="date"
                  className={`form-input ${errors.date_incident ? 'border-red-500' : ''}`}
                  value={formData.date_incident}
                  onChange={(e) => handleInputChange('date_incident', e.target.value)}
                />
                {errors.date_incident && (
                  <p className="text-red-500 text-sm mt-1">{errors.date_incident}</p>
                )}
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Description / الوصف
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">
                  Description en Français *
                </label>
                <textarea
                  className={`form-input h-32 ${errors.description_fr ? 'border-red-500' : ''}`}
                  value={formData.description_fr}
                  onChange={(e) => handleInputChange('description_fr', e.target.value)}
                  placeholder="Décrivez les faits en détail..."
                />
                {errors.description_fr && (
                  <p className="text-red-500 text-sm mt-1">{errors.description_fr}</p>
                )}
              </div>

              <div>
                <label className="form-label text-arabic">
                  الوصف بالعربية *
                </label>
                <textarea
                  className="form-input h-32 text-arabic"
                  value={formData.description_ar}
                  onChange={(e) => handleInputChange('description_ar', e.target.value)}
                  placeholder="اكتب وصف مفصل للحادث..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Informations Supplémentaires / معلومات إضافية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">
                  Notes sur les Preuves / ملاحظات الأدلة
                </label>
                <textarea
                  className="form-input h-24"
                  value={formData.evidence_notes}
                  onChange={(e) => handleInputChange('evidence_notes', e.target.value)}
                  placeholder="Documents, témoins, preuves physiques..."
                />
              </div>

              <div>
                <label className="form-label">
                  Notes de l'Officier / ملاحظات الضابط
                </label>
                <textarea
                  className="form-input h-24"
                  value={formData.officer_notes}
                  onChange={(e) => handleInputChange('officer_notes', e.target.value)}
                  placeholder="Observations et remarques..."
                />
              </div>

              <div>
                <label className="form-label">
                  Officier Responsable / الضابط المسؤول *
                </label>
                <input
                  type="text"
                  className={`form-input ${errors.officer_in_charge ? 'border-red-500' : ''}`}
                  value={formData.officer_in_charge}
                  onChange={(e) => handleInputChange('officer_in_charge', e.target.value)}
                  placeholder="Nom de l'officier en charge"
                />
                {errors.officer_in_charge && (
                  <p className="text-red-500 text-sm mt-1">{errors.officer_in_charge}</p>
                )}
              </div>

              <div>
                <label className="form-label">
                  Statut / الحالة
                </label>
                <select
                  className="form-input"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <option value="pending">En Attente / في الانتظار</option>
                  <option value="investigating">En Investigation / قيد التحقيق</option>
                  <option value="resolved">Résolue / محلولة</option>
                  <option value="closed">Fermée / مغلقة</option>
                </select>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-6 border-t border-gray-200">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex items-center"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <FileText className="w-4 h-4 mr-2" />
              )}
              {isLoading ? 'Enregistrement et génération PDF...' : 'Enregistrer et Exporter PDF'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default NewComplaintForm
