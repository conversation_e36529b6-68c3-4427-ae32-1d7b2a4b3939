import { app as l, <PERSON><PERSON>erWindow as R, ipcMain as m } from "electron";
import { createRequire as D } from "node:module";
import { fileURLToPath as L } from "node:url";
import a from "node:path";
const E = D(import.meta.url), T = a.dirname(L(import.meta.url));
let o = null;
function N() {
  return new Promise(async (t, e) => {
    try {
      const n = E("sql.js"), r = E("fs"), c = l.getPath("userData"), i = a.join(c, "police_complaints.db");
      console.log("Database path:", i);
      const d = a.dirname(i);
      r.existsSync(d) || (r.mkdirSync(d, { recursive: !0 }), console.log("Created directory:", d));
      const _ = await n({
        locateFile: (p) => {
          if (l.isPackaged) {
            const y = [
              // Standard asar.unpacked structure
              a.join(process.resourcesPath, "app.asar.unpacked", "dist-electron", p),
              // Manual build structure
              a.join(process.resourcesPath, "app", "dist-electron", p),
              // Alternative structure
              a.join(T, p),
              // Fallback to current directory
              a.join(process.cwd(), "dist-electron", p)
            ], S = E("fs");
            for (const g of y)
              if (S.existsSync(g))
                return console.log("Found WASM file at:", g), g;
            return console.error("WASM file not found in any expected location"), console.error("Searched paths:", y), y[0];
          }
          return a.join(T, "..", "node_modules", "sql.js", "dist", p);
        }
      });
      let u = null;
      r.existsSync(i) && (u = r.readFileSync(i)), o = new _.Database(u), o.run("PRAGMA foreign_keys = ON"), o.run(`
        CREATE TABLE IF NOT EXISTS complaint_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name_fr TEXT NOT NULL,
          name_ar TEXT NOT NULL,
          color_code TEXT NOT NULL DEFAULT '#3b82f6',
          is_active INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `), o.run(`
        CREATE TABLE IF NOT EXISTS complaints (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          complaint_number TEXT UNIQUE NOT NULL,
          date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
          complainant_name TEXT NOT NULL,
          complainant_address TEXT,
          complainant_phone TEXT,
          complainant_id_number TEXT,
          accused_name TEXT,
          complaint_type_id INTEGER NOT NULL,
          description_ar TEXT,
          description_fr TEXT,
          location_incident TEXT NOT NULL,
          date_incident DATE NOT NULL,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          evidence_notes TEXT,
          officer_notes TEXT,
          officer_in_charge TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (complaint_type_id) REFERENCES complaint_types (id)
        )
      `);
      try {
        o.run("ALTER TABLE complaints ADD COLUMN accused_name TEXT"), console.log("Added accused_name column to existing database");
      } catch {
        console.log("accused_name column already exists or table is new");
      }
      const h = [
        [1, "Vol", "سرقة", "#ef4444"],
        [2, "Agression", "اعتداء", "#f97316"],
        [3, "Fraude", "احتيال", "#eab308"],
        [4, "Harcèlement", "مضايقة", "#8b5cf6"],
        [5, "Dispute", "نزاع", "#06b6d4"],
        [6, "Vandalisme", "تخريب", "#84cc16"],
        [7, "Autre", "أخرى", "#6b7280"]
      ];
      for (const p of h)
        o.run("INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES (?, ?, ?, ?)", p);
      f(), console.log("Database initialized successfully with sql.js"), t(!0);
    } catch (n) {
      console.error("Failed to initialize database:", n), e(n);
    }
  });
}
function f() {
  if (o)
    try {
      const t = E("fs"), e = l.getPath("userData"), n = a.join(e, "police_complaints.db"), r = o.export();
      t.writeFileSync(n, r), console.log("Database saved to:", n);
    } catch (t) {
      console.error("Error saving database:", t);
    }
}
function A() {
  return new Promise((t, e) => {
    try {
      if (!o) throw new Error("Database not initialized");
      const n = /* @__PURE__ */ new Date(), r = n.getFullYear(), c = String(n.getMonth() + 1).padStart(2, "0"), i = String(n.getDate()).padStart(2, "0"), d = `${r}-${c}-${i}`, u = o.prepare(`
        SELECT COUNT(*) as count
        FROM complaints
        WHERE DATE(date_registered) = DATE(?)
      `).getAsObject([d]), h = String((u.count || 0) + 1).padStart(3, "0");
      t(`PL${r}${c}${i}${h}`);
    } catch (n) {
      e(n);
    }
  });
}
process.env.APP_ROOT = a.join(T, "..");
const b = process.env.VITE_DEV_SERVER_URL, v = a.join(process.env.APP_ROOT, "dist-electron"), w = a.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = b ? a.join(process.env.APP_ROOT, "public") : w;
let s;
function O() {
  s = new R({
    title: "Police Complaint System - République du Tchad",
    width: 1200,
    height: 800,
    minWidth: 1e3,
    minHeight: 600,
    icon: a.join(process.env.VITE_PUBLIC, "police-logo.svg"),
    webPreferences: {
      preload: a.join(T, "preload.mjs"),
      nodeIntegration: !1,
      contextIsolation: !0,
      webSecurity: !0,
      allowRunningInsecureContent: !1,
      experimentalFeatures: !1
    },
    show: !1,
    // Don't show until ready
    autoHideMenuBar: !0
    // Hide menu bar for cleaner look
  }), s.once("ready-to-show", () => {
    s == null || s.show(), s && s.focus();
  }), s.webContents.on("will-navigate", (t, e) => {
    const n = new URL(e);
    n.origin !== "http://localhost:5173" && n.origin !== "file://" && t.preventDefault();
  }), s.webContents.setWindowOpenHandler(() => ({ action: "deny" })), s.webContents.on("did-finish-load", () => {
    s == null || s.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  }), b ? (s.loadURL(b), s.webContents.openDevTools()) : s.loadFile(a.join(w, "index.html"));
}
l.on("window-all-closed", () => {
  process.platform !== "darwin" && (l.quit(), s = null);
});
l.on("activate", () => {
  R.getAllWindows().length === 0 && O();
});
l.commandLine.appendSwitch("disable-background-timer-throttling");
l.commandLine.appendSwitch("disable-renderer-backgrounding");
l.commandLine.appendSwitch("disable-backgrounding-occluded-windows");
l.on("web-contents-created", (t, e) => {
  e.setWindowOpenHandler(() => ({ action: "deny" }));
});
l.whenReady().then(async () => {
  try {
    await N(), console.log("Database initialized successfully");
  } catch (t) {
    console.error("Failed to initialize database:", t);
    const { dialog: e } = E("electron");
    e.showErrorBox(
      "Database Error",
      "Failed to initialize the database. The application may not function properly."
    );
  }
  O(), C(), I();
});
function I() {
  const t = E("fs"), e = E("os"), n = a.join(e.homedir(), "Documents", "PoliceReports"), r = a.join(n, "Reports"), c = a.join(n, "Backups");
  try {
    t.existsSync(n) || t.mkdirSync(n, { recursive: !0 }), t.existsSync(r) || t.mkdirSync(r, { recursive: !0 }), t.existsSync(c) || t.mkdirSync(c, { recursive: !0 }), console.log("Default directories created successfully");
  } catch (i) {
    console.error("Failed to create default directories:", i);
  }
}
function C() {
  m.handle("db:getComplaintTypes", async () => {
    try {
      if (!o) throw new Error("Database not initialized");
      const t = o.prepare("SELECT * FROM complaint_types WHERE is_active = 1 ORDER BY name_fr"), e = [];
      for (; t.step(); )
        e.push(t.getAsObject());
      return t.free(), e;
    } catch (t) {
      throw console.error("Error getting complaint types:", t), t;
    }
  }), m.handle("db:createComplaint", async (t, e) => {
    try {
      if (!o) throw new Error("Database not initialized");
      e.complaint_number || (e.complaint_number = await A()), o.run(`
        INSERT INTO complaints (
          complaint_number, complainant_name, complainant_address, complainant_phone,
          complainant_id_number, accused_name, complaint_type_id, description_ar, description_fr,
          location_incident, date_incident, status, priority, evidence_notes,
          officer_notes, officer_in_charge
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        e.complaint_number,
        e.complainant_name,
        e.complainant_address,
        e.complainant_phone,
        e.complainant_id_number,
        e.accused_name,
        e.complaint_type_id,
        e.description_ar,
        e.description_fr,
        e.location_incident,
        e.date_incident,
        e.status,
        e.priority,
        e.evidence_notes,
        e.officer_notes,
        e.officer_in_charge
      ]);
      const n = o.prepare("SELECT last_insert_rowid() as id"), r = n.getAsObject();
      return n.free(), f(), { id: r.id, ...e };
    } catch (n) {
      throw console.error("Error creating complaint:", n), n;
    }
  }), m.handle("db:getComplaints", async () => {
    try {
      if (!o) throw new Error("Database not initialized");
      const t = o.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        ORDER BY c.date_registered DESC
      `), e = [];
      for (; t.step(); )
        e.push(t.getAsObject());
      return t.free(), e;
    } catch (t) {
      throw console.error("Error getting complaints:", t), t;
    }
  }), m.handle("db:getComplaintById", async (t, e) => {
    try {
      if (!o) throw new Error("Database not initialized");
      const n = o.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        WHERE c.id = ?
      `), r = n.step() ? n.getAsObject([e]) : null;
      return n.free(), r;
    } catch (n) {
      throw console.error("Error getting complaint by ID:", n), n;
    }
  }), m.handle("db:updateComplaint", async (t, e, n) => {
    try {
      if (!o) throw new Error("Database not initialized");
      const r = Object.keys(n).map((i) => `${i} = ?`).join(", "), c = [...Object.values(n), e];
      return o.run(`UPDATE complaints SET ${r} WHERE id = ?`, c), f(), !0;
    } catch (r) {
      throw console.error("Error updating complaint:", r), r;
    }
  }), m.handle("db:getComplaintStats", async () => {
    try {
      if (!o) throw new Error("Database not initialized");
      const t = o.prepare(`
        SELECT status, COUNT(*) as count
        FROM complaints
        GROUP BY status
      `), e = [];
      for (; t.step(); )
        e.push(t.getAsObject());
      t.free();
      const n = o.prepare(`
        SELECT ct.name_fr, COUNT(*) as count
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        GROUP BY c.complaint_type_id, ct.name_fr
      `), r = [];
      for (; n.step(); )
        r.push(n.getAsObject());
      n.free();
      const c = o.prepare(`
        SELECT
          strftime('%Y-%m', date_registered) as month,
          COUNT(*) as count
        FROM complaints
        WHERE date_registered >= date('now', '-12 months')
        GROUP BY strftime('%Y-%m', date_registered)
        ORDER BY month
      `), i = [];
      for (; c.step(); )
        i.push(c.getAsObject());
      c.free();
      const d = o.prepare("SELECT COUNT(*) as count FROM complaints"), _ = d.getAsObject();
      d.free();
      const u = _.count || 0;
      return {
        statusStats: e,
        typeStats: r,
        monthlyStats: i,
        total: u
      };
    } catch (t) {
      throw console.error("Error getting complaint stats:", t), t;
    }
  });
}
l.on("before-quit", () => {
  if (o) {
    try {
      f(), o.close(), console.log("Database saved and closed");
    } catch (t) {
      console.error("Error closing database:", t);
    }
    o = null;
  }
});
export {
  v as MAIN_DIST,
  w as RENDERER_DIST,
  b as VITE_DEV_SERVER_URL
};
