@echo off
echo Starting manual build process...

REM Clean previous builds
echo Cleaning previous builds...
if exist "manual-build" rmdir /s /q "manual-build" 2>nul
if exist "build-output" rmdir /s /q "build-output" 2>nul
if exist "release" rmdir /s /q "release" 2>nul

REM Create build directory
echo Creating build directory...
mkdir "manual-build"
mkdir "manual-build\Police-Complaint-System"

REM Copy Electron binaries
echo Copying Electron binaries...
xcopy /E /I /Y "node_modules\electron\dist\*" "manual-build\Police-Complaint-System\"

REM Copy application files
echo Copying application files...
xcopy /E /I /Y "dist\*" "manual-build\Police-Complaint-System\resources\app\dist\"
xcopy /E /I /Y "dist-electron\*" "manual-build\Police-Complaint-System\resources\app\dist-electron\"
copy "package.json" "manual-build\Police-Complaint-System\resources\app\"

REM Copy required node_modules for production
echo Copying required node_modules...
mkdir "manual-build\Police-Complaint-System\resources\app\node_modules" 2>nul
xcopy /E /I /Y "node_modules\sql.js" "manual-build\Police-Complaint-System\resources\app\node_modules\sql.js\"

REM Rename electron.exe to our app name
echo Renaming executable...
if exist "manual-build\Police-Complaint-System\electron.exe" (
    ren "manual-build\Police-Complaint-System\electron.exe" "Police-Complaint-System.exe"
)

REM Create a simple launcher script
echo Creating launcher...
echo @echo off > "manual-build\Police-Complaint-System\start.bat"
echo cd /d "%%~dp0" >> "manual-build\Police-Complaint-System\start.bat"
echo "Police-Complaint-System.exe" >> "manual-build\Police-Complaint-System\start.bat"

echo Manual build completed!
echo Application is in: manual-build\Police-Complaint-System\
echo Run Police-Complaint-System.exe to start the application
pause
