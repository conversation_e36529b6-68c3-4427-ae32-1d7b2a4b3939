{"plugins": ["plugins/markdown"], "source": {"include": ["src/api.js"]}, "opts": {"encoding": "utf8", "destination": "./documentation/", "readme": "documentation_index.md", "template": "./node_modules/clean-jsdoc-theme", "theme_opts": {"title": "sql.js", "meta": ["<title>sql.js API documentation</title>", "<meta name=\"author\" content=\"<PERSON><PERSON>\">", "<meta name=\"description\" content=\"Documentation for sql.js: an in-memory SQL database for the browser based on SQLite.\">"], "menu": [{"title": "Website", "link": "https://sql.js.org/"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://github.com/sql-js/sql.js"}, {"title": "Demo", "link": "https://sql.js.org/examples/GUI/"}]}}}