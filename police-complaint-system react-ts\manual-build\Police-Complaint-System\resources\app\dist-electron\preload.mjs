"use strict";const n=require("electron");n.contextBridge.exposeInMainWorld("ipc<PERSON>ender<PERSON>",{on(...e){const[t,r]=e;return n.ipcRenderer.on(t,(i,...o)=>r(i,...o))},off(...e){const[t,...r]=e;return n.ipcRenderer.off(t,...r)},send(...e){const[t,...r]=e;return n.ipcRenderer.send(t,...r)},invoke(...e){const[t,...r]=e;return n.ipcRenderer.invoke(t,...r)}});n.contextBridge.exposeInMainWorld("electronAPI",{getComplaintTypes:()=>n.ipcRenderer.invoke("db:getComplaintTypes"),createComplaint:e=>n.ipcRenderer.invoke("db:createComplaint",e),getComplaints:()=>n.ipcRenderer.invoke("db:getComplaints"),getComplaintById:e=>n.ipcRenderer.invoke("db:getComplaintById",e),updateComplaint:(e,t)=>n.ipcRenderer.invoke("db:updateComplaint",e,t),getComplaintStats:()=>n.ipcRenderer.invoke("db:getComplaintStats")});
