{"version": 3, "file": "Walker.js", "sourceRoot": "", "sources": ["../src/Walker.ts"], "names": [], "mappings": ";;;AAAA,+BAA+B;AAC/B,+BAA+B;AAC/B,6BAA6B;AAE7B,yCAAmE;AACnE,2DAAuD;AAgBvD,MAAM,CAAC,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAElC,MAAa,MAAM;IAKjB,YAAY,UAAkB;QAHtB,YAAO,GAAa,EAAE,CAAC;QACvB,gBAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;QAuIrC,UAAK,GAA6B,IAAI,CAAC;QApI7C,IAAI,CAAC,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YACjD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QACD,CAAC,CAAC,mCAAmC,UAAU,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAEO,cAAc,CAAC,QAAgB,EAAE,UAAkB;QACzD,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IAC5D,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACxD,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC/B,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,EAAE,CAAC,YAAY;gBAAE,EAAE,CAAC,YAAY,GAAG,EAAE,CAAC;YAC3C,IAAI,CAAC,EAAE,CAAC,eAAe;gBAAE,EAAE,CAAC,eAAe,GAAG,EAAE,CAAC;YACjD,IAAI,CAAC,EAAE,CAAC,oBAAoB;gBAAE,EAAE,CAAC,oBAAoB,GAAG,EAAE,CAAC;YAC3D,OAAO,EAAE,CAAC;SACX;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,iCAAiC,CAAC,UAAkB,EAAE,UAAkB,EAAE,OAAgB;QACtG,IAAI,QAAQ,GAAG,UAAU,CAAC;QAC1B,IAAI,cAAc,GAAkB,IAAI,CAAC;QACzC,IAAI,YAAY,GAAkB,IAAI,CAAC;QACvC,sDAAsD;QACtD,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,YAAY,EAAE;YACpF,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACzD,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;gBACrC,cAAc,GAAG,YAAY,CAAC;aAC/B;iBAAM;gBACL,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,cAAc,EAAE;oBAC5D,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;iBACnC;gBACD,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;aACjD;SACF;QACD,uDAAuD;QACvD,IAAI,CAAC,cAAc,IAAI,OAAO,KAAK,kBAAO,CAAC,QAAQ,IAAI,OAAO,KAAK,kBAAO,CAAC,YAAY,EAAE;YACvF,MAAM,IAAI,KAAK,CACb,4BAA4B,UAAU,WAAW,UAAU;;oLAEiH,CAC7K,CAAC;SACH;QACD,4DAA4D;QAC5D,IAAI,cAAc,EAAE;YAClB,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SAC/D;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,UAAkB,EAAE,EAAe;QACtE,IAAI,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAC,EAAE;YACvC,OAAO,oCAAgB,CAAC,QAAQ,CAAA;SACjC;aAAM,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,EAAE;YACpE,OAAO,oCAAgB,CAAC,QAAQ,CAAA;SACjC;QACD,OAAO,oCAAgB,CAAC,IAAI,CAAA;IAC9B,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,UAAkB,EAAE,OAAgB;QAC1E,CAAC,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9D,wCAAwC;QACxC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACpC,CAAC,CAAC,2BAA2B,CAAC,CAAC;YAC/B,qCAAqC;YACrC,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAE,MAAM,CAAC,IAAI,KAAK,UAAU,CAAW,CAAC;YAC1F,+DAA+D;YAC/D,mEAAmE;YACnE,IAAI,IAAA,yBAAc,EAAC,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,EAAE;gBACnD,CAAC,CAAC,kCAAkC,cAAc,CAAC,OAAO,gCAAgC,OAAO,sBAAsB,CAAC,CAAC;gBACzH,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;aAClC;YACD,OAAO;SACR;QAED,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAClD,kEAAkE;QAClE,8DAA8D;QAC9D,IAAI,CAAC,EAAE,EAAE;YACP,CAAC,CAAC,gDAAgD,CAAC,CAAC;YACpD,OAAO;SACR;QAED,wCAAwC;QACxC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAChB,OAAO;YACP,gBAAgB,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,EAAE,CAAC;YACnE,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,EAAE,CAAC,IAAI;SACd,CAAC,CAAC;QAEH,qBAAqB;QACrB,KAAK,MAAM,UAAU,IAAI,EAAE,CAAC,YAAY,EAAE;YACxC,4FAA4F;YAC5F,kDAAkD;YAClD,IAAI,UAAU,IAAI,EAAE,CAAC,oBAAoB,EAAE;gBACzC,CAAC,CAAC,SAAS,UAAU,oBAAoB,UAAU,iCAAiC,CAAC,CAAC;gBACtF,SAAS;aACV;YACD,MAAM,IAAI,CAAC,iCAAiC,CAC1C,UAAU,EACV,UAAU,EACV,IAAA,uBAAY,EAAC,OAAO,EAAE,kBAAO,CAAC,IAAI,CAAC,CACpC,CAAC;SACH;QAED,yBAAyB;QACzB,KAAK,MAAM,UAAU,IAAI,EAAE,CAAC,oBAAoB,EAAE;YAChD,MAAM,IAAI,CAAC,iCAAiC,CAC1C,UAAU,EACV,UAAU,EACV,IAAA,uBAAY,EAAC,OAAO,EAAE,kBAAO,CAAC,QAAQ,CAAC,CACxC,CAAC;SACH;QAED,2DAA2D;QAC3D,IAAI,OAAO,KAAK,kBAAO,CAAC,IAAI,EAAE;YAC5B,CAAC,CAAC,2DAA2D,CAAC,CAAC;YAC/D,KAAK,MAAM,UAAU,IAAI,EAAE,CAAC,eAAe,EAAE;gBAC3C,MAAM,IAAI,CAAC,iCAAiC,CAC1C,UAAU,EACV,UAAU,EACV,IAAA,uBAAY,EAAC,OAAO,EAAE,kBAAO,CAAC,GAAG,CAAC,CACnC,CAAC;aACH;SACF;IACH,CAAC;IAGD,KAAK,CAAC,QAAQ;QACZ,CAAC,CAAC,oBAAoB,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,CAAW,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3D,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;gBAClB,IAAI;oBACF,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,kBAAO,CAAC,IAAI,CAAC,CAAC;iBACrE;gBAAC,OAAO,GAAG,EAAE;oBACZ,MAAM,CAAC,GAAG,CAAC,CAAC;oBACZ,OAAO;iBACR;gBACD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,CAAC,CAAC,kFAAkF,CAAC,CAAC;SACvF;QACD,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF;AAjKD,wBAiKC"}