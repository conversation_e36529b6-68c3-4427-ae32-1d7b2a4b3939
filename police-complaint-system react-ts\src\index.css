@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for police application */
@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-gray-900 font-french;
    margin: 0;
    min-height: 100vh;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  /* Arabic text styling */
  .text-arabic {
    @apply font-arabic text-right;
    direction: rtl;
  }

  /* French text styling */
  .text-french {
    @apply font-french text-left;
    direction: ltr;
  }

  /* Bilingual container */
  .bilingual-container {
    @apply grid grid-cols-2 gap-4;
  }

  /* Police header styling */
  .police-header {
    @apply bg-gradient-to-r from-blue-900 to-blue-700 text-white p-4 shadow-lg;
  }

  /* Form styling */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }

  /* Status badges */
  .status-pending {
    @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-investigating {
    @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-resolved {
    @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-closed {
    @apply bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Priority badges */
  .priority-low {
    @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .priority-medium {
    @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .priority-high {
    @apply bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .priority-urgent {
    @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Card styling */
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }

  /* Navigation styling */
  .nav-item {
    @apply flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-md transition-colors duration-200;
  }

  .nav-item.active {
    @apply bg-blue-100 text-blue-900 font-medium;
  }
}
