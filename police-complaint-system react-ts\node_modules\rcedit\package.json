{"name": "rcedit", "version": "3.1.0", "description": "Node module to edit resources of exe", "main": "lib/rcedit.js", "types": "lib/index.d.ts", "files": ["bin", "lib/index.d.ts"], "scripts": {"docs:build": "node script/build-docs.js", "mocha": "mocha test/*.js", "test": "npm run lint && npm run tsd && npm run mocha", "lint": "npm run lint:js && npm run lint:ts", "lint:js": "standard", "lint:ts": "ts-standard", "tsd": "tsd"}, "repository": {"type": "git", "url": "https://github.com/electron/node-rcedit.git"}, "bugs": {"url": "https://github.com/electron/node-rcedit/issues"}, "license": "MIT", "engines": {"node": ">= 10.0.0"}, "dependencies": {"cross-spawn-windows-exe": "^1.1.0"}, "devDependencies": {"@continuous-auth/semantic-release-npm": "^2.0.0", "got": "^11.8.0", "mocha": "^8.2.1", "standard": "^16.0.3", "temp": "^0.9.4", "ts-standard": "^10.0.0", "tsd": "^0.14.0", "typedoc": "^0.20.0-beta.27", "typescript": "^4.1.2"}, "tsd": {"directory": "test"}}