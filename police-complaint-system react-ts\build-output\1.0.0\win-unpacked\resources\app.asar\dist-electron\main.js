import { app as l, <PERSON><PERSON><PERSON><PERSON><PERSON>ow as g, ipc<PERSON><PERSON> as E } from "electron";
import { createRequire as w } from "node:module";
import { fileURLToPath as O } from "node:url";
import s from "node:path";
const m = w(import.meta.url), y = s.dirname(O(import.meta.url));
let r = null;
function S() {
  return new Promise(async (t, e) => {
    try {
      const n = m("sql.js"), o = m("fs"), c = l.getPath("userData"), i = s.join(c, "police_complaints.db");
      console.log("Database path:", i);
      const d = s.dirname(i);
      o.existsSync(d) || (o.mkdirSync(d, { recursive: !0 }), console.log("Created directory:", d));
      const _ = await n({
        locateFile: (u) => l.isPackaged ? s.join(process.resourcesPath, "app.asar.unpacked", "dist-electron", u) : s.join(y, "..", "node_modules", "sql.js", "dist", u)
      });
      let p = null;
      o.existsSync(i) && (p = o.readFileSync(i)), r = new _.Database(p), r.run("PRAGMA foreign_keys = ON"), r.run(`
        CREATE TABLE IF NOT EXISTS complaint_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name_fr TEXT NOT NULL,
          name_ar TEXT NOT NULL,
          color_code TEXT NOT NULL DEFAULT '#3b82f6',
          is_active INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `), r.run(`
        CREATE TABLE IF NOT EXISTS complaints (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          complaint_number TEXT UNIQUE NOT NULL,
          date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
          complainant_name TEXT NOT NULL,
          complainant_address TEXT,
          complainant_phone TEXT,
          complainant_id_number TEXT,
          accused_name TEXT,
          complaint_type_id INTEGER NOT NULL,
          description_ar TEXT,
          description_fr TEXT,
          location_incident TEXT NOT NULL,
          date_incident DATE NOT NULL,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          evidence_notes TEXT,
          officer_notes TEXT,
          officer_in_charge TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (complaint_type_id) REFERENCES complaint_types (id)
        )
      `);
      try {
        r.run("ALTER TABLE complaints ADD COLUMN accused_name TEXT"), console.log("Added accused_name column to existing database");
      } catch {
        console.log("accused_name column already exists or table is new");
      }
      const f = [
        [1, "Vol", "سرقة", "#ef4444"],
        [2, "Agression", "اعتداء", "#f97316"],
        [3, "Fraude", "احتيال", "#eab308"],
        [4, "Harcèlement", "مضايقة", "#8b5cf6"],
        [5, "Dispute", "نزاع", "#06b6d4"],
        [6, "Vandalisme", "تخريب", "#84cc16"],
        [7, "Autre", "أخرى", "#6b7280"]
      ];
      for (const u of f)
        r.run("INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES (?, ?, ?, ?)", u);
      T(), console.log("Database initialized successfully with sql.js"), t(!0);
    } catch (n) {
      console.error("Failed to initialize database:", n), e(n);
    }
  });
}
function T() {
  if (r)
    try {
      const t = m("fs"), e = l.getPath("userData"), n = s.join(e, "police_complaints.db"), o = r.export();
      t.writeFileSync(n, o), console.log("Database saved to:", n);
    } catch (t) {
      console.error("Error saving database:", t);
    }
}
function D() {
  return new Promise((t, e) => {
    try {
      if (!r) throw new Error("Database not initialized");
      const n = /* @__PURE__ */ new Date(), o = n.getFullYear(), c = String(n.getMonth() + 1).padStart(2, "0"), i = String(n.getDate()).padStart(2, "0"), d = `${o}-${c}-${i}`, p = r.prepare(`
        SELECT COUNT(*) as count
        FROM complaints
        WHERE DATE(date_registered) = DATE(?)
      `).getAsObject([d]), f = String((p.count || 0) + 1).padStart(3, "0");
      t(`PL${o}${c}${i}${f}`);
    } catch (n) {
      e(n);
    }
  });
}
process.env.APP_ROOT = s.join(y, "..");
const h = process.env.VITE_DEV_SERVER_URL, P = s.join(process.env.APP_ROOT, "dist-electron"), b = s.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = h ? s.join(process.env.APP_ROOT, "public") : b;
let a;
function R() {
  a = new g({
    title: "Police Complaint System - République du Tchad",
    width: 1200,
    height: 800,
    minWidth: 1e3,
    minHeight: 600,
    icon: s.join(process.env.VITE_PUBLIC, "police-logo.svg"),
    webPreferences: {
      preload: s.join(y, "preload.mjs"),
      nodeIntegration: !1,
      contextIsolation: !0,
      webSecurity: !0,
      allowRunningInsecureContent: !1,
      experimentalFeatures: !1
    },
    show: !1,
    // Don't show until ready
    autoHideMenuBar: !0
    // Hide menu bar for cleaner look
  }), a.once("ready-to-show", () => {
    a == null || a.show(), a && a.focus();
  }), a.webContents.on("will-navigate", (t, e) => {
    const n = new URL(e);
    n.origin !== "http://localhost:5173" && n.origin !== "file://" && t.preventDefault();
  }), a.webContents.setWindowOpenHandler(() => ({ action: "deny" })), a.webContents.on("did-finish-load", () => {
    a == null || a.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  }), h ? (a.loadURL(h), a.webContents.openDevTools()) : a.loadFile(s.join(b, "index.html"));
}
l.on("window-all-closed", () => {
  process.platform !== "darwin" && (l.quit(), a = null);
});
l.on("activate", () => {
  g.getAllWindows().length === 0 && R();
});
l.commandLine.appendSwitch("disable-background-timer-throttling");
l.commandLine.appendSwitch("disable-renderer-backgrounding");
l.commandLine.appendSwitch("disable-backgrounding-occluded-windows");
l.on("web-contents-created", (t, e) => {
  e.setWindowOpenHandler(() => ({ action: "deny" }));
});
l.whenReady().then(async () => {
  try {
    await S(), console.log("Database initialized successfully");
  } catch (t) {
    console.error("Failed to initialize database:", t);
    const { dialog: e } = m("electron");
    e.showErrorBox(
      "Database Error",
      "Failed to initialize the database. The application may not function properly."
    );
  }
  R(), N(), L();
});
function L() {
  const t = m("fs"), e = m("os"), n = s.join(e.homedir(), "Documents", "PoliceReports"), o = s.join(n, "Reports"), c = s.join(n, "Backups");
  try {
    t.existsSync(n) || t.mkdirSync(n, { recursive: !0 }), t.existsSync(o) || t.mkdirSync(o, { recursive: !0 }), t.existsSync(c) || t.mkdirSync(c, { recursive: !0 }), console.log("Default directories created successfully");
  } catch (i) {
    console.error("Failed to create default directories:", i);
  }
}
function N() {
  E.handle("db:getComplaintTypes", async () => {
    try {
      if (!r) throw new Error("Database not initialized");
      const t = r.prepare("SELECT * FROM complaint_types WHERE is_active = 1 ORDER BY name_fr"), e = [];
      for (; t.step(); )
        e.push(t.getAsObject());
      return t.free(), e;
    } catch (t) {
      throw console.error("Error getting complaint types:", t), t;
    }
  }), E.handle("db:createComplaint", async (t, e) => {
    try {
      if (!r) throw new Error("Database not initialized");
      e.complaint_number || (e.complaint_number = await D()), r.run(`
        INSERT INTO complaints (
          complaint_number, complainant_name, complainant_address, complainant_phone,
          complainant_id_number, accused_name, complaint_type_id, description_ar, description_fr,
          location_incident, date_incident, status, priority, evidence_notes,
          officer_notes, officer_in_charge
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        e.complaint_number,
        e.complainant_name,
        e.complainant_address,
        e.complainant_phone,
        e.complainant_id_number,
        e.accused_name,
        e.complaint_type_id,
        e.description_ar,
        e.description_fr,
        e.location_incident,
        e.date_incident,
        e.status,
        e.priority,
        e.evidence_notes,
        e.officer_notes,
        e.officer_in_charge
      ]);
      const n = r.prepare("SELECT last_insert_rowid() as id"), o = n.getAsObject();
      return n.free(), T(), { id: o.id, ...e };
    } catch (n) {
      throw console.error("Error creating complaint:", n), n;
    }
  }), E.handle("db:getComplaints", async () => {
    try {
      if (!r) throw new Error("Database not initialized");
      const t = r.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        ORDER BY c.date_registered DESC
      `), e = [];
      for (; t.step(); )
        e.push(t.getAsObject());
      return t.free(), e;
    } catch (t) {
      throw console.error("Error getting complaints:", t), t;
    }
  }), E.handle("db:getComplaintById", async (t, e) => {
    try {
      if (!r) throw new Error("Database not initialized");
      const n = r.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        WHERE c.id = ?
      `), o = n.step() ? n.getAsObject([e]) : null;
      return n.free(), o;
    } catch (n) {
      throw console.error("Error getting complaint by ID:", n), n;
    }
  }), E.handle("db:updateComplaint", async (t, e, n) => {
    try {
      if (!r) throw new Error("Database not initialized");
      const o = Object.keys(n).map((i) => `${i} = ?`).join(", "), c = [...Object.values(n), e];
      return r.run(`UPDATE complaints SET ${o} WHERE id = ?`, c), T(), !0;
    } catch (o) {
      throw console.error("Error updating complaint:", o), o;
    }
  }), E.handle("db:getComplaintStats", async () => {
    try {
      if (!r) throw new Error("Database not initialized");
      const t = r.prepare(`
        SELECT status, COUNT(*) as count
        FROM complaints
        GROUP BY status
      `), e = [];
      for (; t.step(); )
        e.push(t.getAsObject());
      t.free();
      const n = r.prepare(`
        SELECT ct.name_fr, COUNT(*) as count
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        GROUP BY c.complaint_type_id, ct.name_fr
      `), o = [];
      for (; n.step(); )
        o.push(n.getAsObject());
      n.free();
      const c = r.prepare(`
        SELECT
          strftime('%Y-%m', date_registered) as month,
          COUNT(*) as count
        FROM complaints
        WHERE date_registered >= date('now', '-12 months')
        GROUP BY strftime('%Y-%m', date_registered)
        ORDER BY month
      `), i = [];
      for (; c.step(); )
        i.push(c.getAsObject());
      c.free();
      const d = r.prepare("SELECT COUNT(*) as count FROM complaints"), _ = d.getAsObject();
      d.free();
      const p = _.count || 0;
      return {
        statusStats: e,
        typeStats: o,
        monthlyStats: i,
        total: p
      };
    } catch (t) {
      throw console.error("Error getting complaint stats:", t), t;
    }
  });
}
l.on("before-quit", () => {
  if (r) {
    try {
      T(), r.close(), console.log("Database saved and closed");
    } catch (t) {
      console.error("Error closing database:", t);
    }
    r = null;
  }
});
export {
  P as MAIN_DIST,
  b as RENDERER_DIST,
  h as VITE_DEV_SERVER_URL
};
