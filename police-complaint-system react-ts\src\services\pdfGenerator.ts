import jsPD<PERSON> from 'jspdf'
import html2canvas from 'html2canvas'
import { Complaint } from '../database/database'

// Base64 encoded police logo for PDF embedding (no file path dependencies)
const POLICE_LOGO_BASE64 = 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDIwMDEwOTA0Ly9FTiIKICJodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAxL1JFQy1TVkctMjAwMTA5MDQvRFREL3N2ZzEwLmR0ZCI+CjxzdmcgdmVyc2lvbj0iMS4wIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiB3aWR0aD0iMTg4LjAwMDAwMHB0IiBoZWlnaHQ9IjE3Ni4wMDAwMDBwdCIgdmlld0JveD0iMCAwIDE4OC4wMDAwMDAgMTc2LjAwMDAwMCIKIHByZXNlcnZlQXNwZWN0UmF0aW89InhNaWRZTWlkIG1lZXQiPgoKPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4wMDAwMDAsMTc2LjAwMDAwMCkgc2NhbGUoMC4xMDAwMDAsLTAuMTAwMDAwKSIKZmlsbD0iIzAwMDAwMCIgc3Ryb2tlPSJub25lIj4KPHN0eWxlPi5zdDAge2ZpbGw6ICMwMDAwMDA7fTwvc3R5bGU+CjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik04MTUgMTcwOSBjLTE4NyAtMzcgLTM0MiAtMTIzIC00NjUgLTI1NiAtMjYwIC0yODQgLTI5MCAtNzA5IC03MgotMTAzMCA1MyAtNzggMTc1IC0xOTIgMjU4IC0yNDEgMzggLTIzIDExMiAtNTUgMTY0IC03MyA4MiAtMjggMTEyIC0zMyAyMjAKLTM3IDE4MSAtNiAzMTAgMjYgNDY1IDExNiA4NSA1MCAyMzAgMTk5IDI4MiAyOTIgODcgMTU1IDEyMyAzMzAgMTA0IDUwMiAtMTMKMTEyIC0zMiAxNzkgLTc4IDI3NCAtMTAxIDIwNyAtMjg4IDM2NCAtNTA4IDQyOSAtMTAxIDMwIC0yODIgNDIgLTM3MCAyNHoKbTMzMSAtMjkgYzI3MyAtNjkgNDg2IC0yNjggNTc2IC01NDAgMzAgLTg5IDMyIC0xMDUgMzIgLTI0MCAxIC0xMjMgLTMgLTE1NwotMjIgLTIyNCAtNzkgLTI3NCAtMjgzIC00ODAgLTU1NyAtNTYyIC0xMjAgLTM1IC0zMDcgLTM4IC00MjkgLTUgLTEzMiAzNQotMjQ3IDEwMiAtMzUyIDIwNiAtMTA1IDEwNCAtMTU4IDE4OSAtMjA2IDMyNiAtMzEgOTAgLTMyIDEwMSAtMzMgMjQ5IDAgMTc2CjEzIDIzNCA4NSAzNzcgOTkgMTk4IDMwMSAzNTkgNTE3IDQxMiAxMTAgMjcgMjg0IDI4IDM4OSAxeiIvPgo8L2c+Cjwvc3ZnPgo='

// Arabic text processing utilities removed - using HTML-to-canvas approach instead

interface ComplaintWithTypeInfo extends Complaint {
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

export class PDFGenerator {
  private static instance: PDFGenerator
  
  public static getInstance(): PDFGenerator {
    if (!PDFGenerator.instance) {
      PDFGenerator.instance = new PDFGenerator()
    }
    return PDFGenerator.instance
  }

  /**
   * Generate a detailed complaint report in PDF format with proper pagination
   */
  public async generateComplaintReport(complaint: ComplaintWithTypeInfo): Promise<void> {
    try {
      // Try HTML-to-canvas approach with pagination
      await this.generatePaginatedPDF(complaint)
    } catch (error) {
      console.error('Error generating paginated PDF:', error)
      // Fallback to text-based PDF with pagination
      await this.generateTextBasedPDF(complaint)
    }
  }

  /**
   * Generate PDF using HTML-to-canvas with proper pagination
   */
  private async generatePaginatedPDF(complaint: ComplaintWithTypeInfo): Promise<void> {
    // Create HTML content sections
    const sections = this.createPaginatedHTML(complaint)
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()

    for (let i = 0; i < sections.length; i++) {
      if (i > 0) {
        pdf.addPage()
      }

      // Create temporary div for this section
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = sections[i]
      tempDiv.style.position = 'absolute'
      tempDiv.style.left = '-9999px'
      tempDiv.style.width = '210mm'
      tempDiv.style.height = '297mm'
      tempDiv.style.fontFamily = 'Arial, "Noto Naskh Arabic", "Amiri", "Arabic Typesetting", sans-serif'
      tempDiv.style.padding = '20px'
      tempDiv.style.boxSizing = 'border-box'
      tempDiv.style.backgroundColor = '#ffffff'
      document.body.appendChild(tempDiv)

      try {
        // Convert section to canvas
        const canvas = await html2canvas(tempDiv, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: 794,
          height: 1123,
          scrollX: 0,
          scrollY: 0
        })

        // Add to PDF
        const imgData = canvas.toDataURL('image/png')
        pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight)
      } finally {
        document.body.removeChild(tempDiv)
      }
    }

    // Save the PDF
    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Create paginated HTML sections
   */
  private createPaginatedHTML(complaint: ComplaintWithTypeInfo): string[] {
    const sections: string[] = []

    // Page 1: Header + Basic Info + Complainant Info
    sections.push(this.createPage1HTML(complaint))

    // Page 2: Description + Additional Info (if needed)
    const page2Content = this.createPage2HTML(complaint)
    if (page2Content.trim()) {
      sections.push(page2Content)
    }

    return sections
  }

  /**
   * Create first page HTML
   */
  private createPage1HTML(complaint: ComplaintWithTypeInfo): string {
    return `
      <div style="
        font-family: Arial, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      ">
        ${this.getOfficialHeader()}

        <!-- Title -->
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0;">RAPPORT DE PLAINTE</h2>
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0; direction: rtl;">تقرير الشكوى</h2>
        </div>

        <!-- Complaint Info -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px;">
          <div>
            <strong>Numéro:</strong> ${complaint.complaint_number}<br>
            <strong>Date:</strong> ${this.formatDate(complaint.date_registered || '')}
          </div>
          <div style="text-align: right; direction: rtl;">
            <strong>رقم:</strong> ${complaint.complaint_number}<br>
            <strong>التاريخ:</strong> ${this.formatDate(complaint.date_registered || '')}
          </div>
        </div>

        <!-- General Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS GÉNÉRALES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات عامة</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Type de Plainte:</strong><br>
                ${complaint.type_name_fr || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Lieu de l'Incident:</strong><br>
                ${complaint.location_incident}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Date de l'Incident:</strong><br>
                ${this.formatDate(complaint.date_incident)}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>نوع الشكوى:</strong><br>
                ${complaint.type_name_ar || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>مكان الحادث:</strong><br>
                ${complaint.location_incident}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>تاريخ الحادث:</strong><br>
                ${this.formatDate(complaint.date_incident)}
              </div>
            </div>
          </div>
        </div>

        <!-- Complainant Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS DU PLAIGNANT</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات المشتكي</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Nom Complet:</strong><br>
                ${complaint.complainant_name}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Numéro d'Identité:</strong><br>
                ${complaint.complainant_id_number || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Téléphone:</strong><br>
                ${complaint.complainant_phone || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Adresse:</strong><br>
                ${complaint.complainant_address || 'Non spécifiée'}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>الاسم الكامل:</strong><br>
                ${complaint.complainant_name}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>رقم الهوية:</strong><br>
                ${complaint.complainant_id_number || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>الهاتف:</strong><br>
                ${complaint.complainant_phone || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>العنوان:</strong><br>
                ${complaint.complainant_address || 'غير محدد'}
              </div>
            </div>
          </div>
        </div>

        <!-- Accused Information (Always show section, even if empty) -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS SUR LE MIS EN CAUSE</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات المشتكى عليه</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Nom du Mis en Cause:</strong><br>
                ${(complaint as any).accused_name || '[Non spécifié / À déterminer]'}
              </div>
              ${(complaint as any).accused_name ? `
              <div style="margin-bottom: 8px;">
                <strong>Informations Supplémentaires:</strong><br>
                [À compléter lors de l'enquête]
              </div>
              ` : `
              <div style="margin-bottom: 8px; font-style: italic; color: #666;">
                Les informations sur le mis en cause seront ajoutées<br>
                lors de l'avancement de l'enquête.
              </div>
              `}
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>اسم المشتكى عليه:</strong><br>
                ${(complaint as any).accused_name || '[غير محدد / سيتم تحديده]'}
              </div>
              ${(complaint as any).accused_name ? `
              <div style="margin-bottom: 8px;">
                <strong>معلومات إضافية:</strong><br>
                [سيتم استكمالها أثناء التحقيق]
              </div>
              ` : `
              <div style="margin-bottom: 8px; font-style: italic; color: #666; direction: rtl;">
                سيتم إضافة معلومات المشتكى عليه<br>
                عند تقدم التحقيق.
              </div>
              `}
            </div>
          </div>
        </div>

        <!-- Page Footer -->
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
          <div>Page 1 - Document généré automatiquement par le Système de Gestion des Plaintes</div>
          <div style="direction: rtl; margin-top: 3px;">صفحة 1 - وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى</div>
        </div>
      </div>
    `
  }

  /**
   * Create second page HTML for description and additional info
   */
  private createPage2HTML(complaint: ComplaintWithTypeInfo): string {
    const hasDescription = complaint.description_fr || complaint.description_ar
    const hasNotes = complaint.evidence_notes || complaint.officer_notes

    if (!hasDescription && !hasNotes) {
      return '' // No second page needed
    }

    return `
      <div style="
        font-family: Arial, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      ">
        ${this.getOfficialHeaderSmall()}

        <!-- Description -->
        ${hasDescription ? `
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">DESCRIPTION DE L'INCIDENT</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">وصف الحادث</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <strong>Description en Français:</strong><br>
              <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd; min-height: 100px;">
                ${complaint.description_fr || '[Description non fournie en français]'}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <strong>الوصف بالعربية:</strong><br>
              <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd; text-align: right; direction: rtl; min-height: 100px;">
                ${complaint.description_ar || '[لم يتم تقديم وصف بالعربية]'}
              </div>
            </div>
          </div>
        </div>
        ` : ''}

        ${hasNotes ? `
        <!-- Additional Notes -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">NOTES SUPPLÉMENTAIRES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">ملاحظات إضافية</h3>
          </div>

          ${complaint.evidence_notes ? `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between;">
              <strong>Notes sur les Preuves:</strong>
              <strong style="direction: rtl;">ملاحظات الأدلة:</strong>
            </div>
            <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
              ${complaint.evidence_notes}
            </div>
          </div>
          ` : ''}

          ${complaint.officer_notes ? `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between;">
              <strong>Notes de l'Officier:</strong>
              <strong style="direction: rtl;">ملاحظات الضابط:</strong>
            </div>
            <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
              ${complaint.officer_notes}
            </div>
          </div>
          ` : ''}
        </div>
        ` : ''}

        <!-- Official Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS OFFICIELLES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات رسمية</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Officier Responsable:</strong><br>
                ${complaint.officer_in_charge}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Date de Création:</strong><br>
                ${this.formatDate((complaint.created_at || complaint.date_registered) || '')}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Dernière Modification:</strong><br>
                ${this.formatDate((complaint.updated_at || complaint.date_registered) || '')}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>الضابط المسؤول:</strong><br>
                ${complaint.officer_in_charge}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>تاريخ الإنشاء:</strong><br>
                ${this.formatDate((complaint.created_at || complaint.date_registered) || '')}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>آخر تعديل:</strong><br>
                ${this.formatDate((complaint.updated_at || complaint.date_registered) || '')}
              </div>
            </div>
          </div>
        </div>

        <!-- Signature and Stamp Section -->
        <div style="margin-bottom: 40px; margin-top: 40px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">VALIDATION OFFICIELLE</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">التصديق الرسمي</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 30px;">
            <!-- Signature Section -->
            <div style="text-align: center;">
              <div style="margin-bottom: 15px;">
                <strong style="font-size: 12px;">Le Commissaire</strong><br>
                <span style="font-size: 10px; font-style: italic;">Signature</span>
              </div>
              <div style="
                border: 1px solid #000;
                height: 80px;
                margin: 10px 0;
                background: #f9f9f9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                color: #666;
              ">
                [Signature du Commissaire]
              </div>
            </div>

            <!-- Stamp Section -->
            <div style="text-align: center; direction: rtl;">
              <div style="margin-bottom: 15px;">
                <strong style="font-size: 12px;">المفوض</strong><br>
                <span style="font-size: 10px; font-style: italic;">التوقيع</span>
              </div>
              <div style="
                border: 1px solid #000;
                height: 80px;
                margin: 10px 0;
                background: #f9f9f9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                color: #666;
                direction: rtl;
              ">
                [الختم الرسمي]
              </div>
            </div>
          </div>

          <!-- Official Stamp Area -->
          <div style="text-align: center; margin-top: 30px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <strong style="font-size: 12px;">CACHET OFFICIEL</strong>
              <strong style="font-size: 12px; direction: rtl;">الختم الرسمي</strong>
            </div>
            <div style="
              border: 2px solid #000;
              height: 100px;
              width: 200px;
              margin: 0 auto;
              background: #f9f9f9;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              color: #666;
              border-radius: 50%;
            ">
              [CACHET OFFICIEL<br>DE LA POLICE NATIONALE<br>DU TCHAD]
            </div>
          </div>
        </div>

        <!-- Page Footer -->
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
          <div>Page 2 - Document généré automatiquement par le Système de Gestion des Plaintes</div>
          <div style="direction: rtl; margin-top: 3px;">صفحة 2 - وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى</div>
        </div>
      </div>
    `
  }

  /**
   * Get official header for subsequent pages (smaller)
   */
  private getOfficialHeaderSmall(): string {
    return `
      <div style="margin-bottom: 20px; border-bottom: 1px solid #000; padding-bottom: 10px;">
        <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 15px; align-items: center;">
          <!-- Left Side (French) -->
          <div style="text-align: left; font-size: 9px; line-height: 1.2;">
            <div style="font-weight: bold; font-size: 12px; margin-bottom: 5px;">RÉPUBLIQUE DU TCHAD</div>
            <div style="margin-bottom: 2px;">Direction Générale de la Police Nationale</div>
          </div>

          <!-- Center (Official Logo - Smaller) -->
          <div style="text-align: center;">
            <img src="${POLICE_LOGO_BASE64}" alt="Police Nationale du Tchad" style="
              width: 50px;
              height: 50px;
              margin: 0 auto;
              display: block;
            " />
          </div>

          <!-- Right Side (Arabic) -->
          <div style="text-align: right; font-size: 9px; line-height: 1.2; direction: rtl;">
            <div style="font-weight: bold; font-size: 12px; margin-bottom: 5px;">جمهورية تشاد</div>
            <div style="margin-bottom: 2px;">الإدارة العامة للشرطة الوطنية</div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * Get full official header with real logo
   */
  private getOfficialHeader(): string {
    return `
      <div style="margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 15px;">
        <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 20px; align-items: center;">
          <!-- Left Side (French) -->
          <div style="text-align: left; font-size: 11px; line-height: 1.3;">
            <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">RÉPUBLIQUE DU TCHAD</div>
            <div style="margin-bottom: 3px;">Présidence de la République</div>
            <div style="margin-bottom: 3px;">Ministère de la Sécurité Publique et de l'Immigration</div>
            <div style="margin-bottom: 3px;">Direction Générale de la Police Nationale</div>
            <div style="margin-bottom: 3px;">Direction de la Sécurité Publique</div>
            <div style="margin-bottom: 3px;">Commissariat Central de Sécurité Publique n° _______</div>
            <div>Commissariat de Sécurité Publique n° _______</div>
          </div>

          <!-- Center (Official Logo) -->
          <div style="text-align: center; padding: 0 20px;">
            <img src="data:image/svg+xml;base64,
            PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/
            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"
             alt="Police Nationale du Tchad" style="
              width: 80px;
              height: 80px;
              margin: 0 auto;
              display: block;
            " />
          </div>

          <!-- Right Side (Arabic) -->
          <div style="text-align: right; font-size: 11px; line-height: 1.3; direction: rtl;">
            <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">جمهورية تشاد</div>
            <div style="margin-bottom: 3px;">رئاسة الجمهورية</div>
            <div style="margin-bottom: 3px;">وزارة الأمن العام والهجرة</div>
            <div style="margin-bottom: 3px;">الإدارة العامة للشرطة الوطنية</div>
            <div style="margin-bottom: 3px;">إدارة الأمن العام</div>
            <div style="margin-bottom: 3px;">المفوضية المركزية للأمن العام رقم _______</div>
            <div>مفوضية الأمن العام رقم _______</div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * Text-based PDF generation with pagination (fallback)
   */
  private async generateTextBasedPDF(complaint: ComplaintWithTypeInfo): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    const maxY = pageHeight - margin - 20 // Reserve space for footer
    let yPosition = margin

    // Helper function to check if we need a new page
    const checkNewPage = (requiredSpace: number = 10): void => {
      if (yPosition + requiredSpace > maxY) {
        pdf.addPage()
        yPosition = margin + 20
        // Add small header on new pages
        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'bold')
        pdf.text('RÉPUBLIQUE DU TCHAD - POLICE NATIONALE', pageWidth / 2, margin + 10, { align: 'center' })
        yPosition += 15
      }
    }

    // Page 1: Header and basic info
    this.addTextBasedHeader(pdf, pageWidth, margin)
    yPosition += 50

    // Title
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RAPPORT DE PLAINTE', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Basic info
    pdf.setFontSize(11)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Numéro: ${complaint.complaint_number}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Date: ${this.formatDate(complaint.date_registered || '')}`, margin, yPosition)
    yPosition += 15

    // General Information
    checkNewPage(30)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS GÉNÉRALES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Type: ${complaint.type_name_fr || 'Non spécifié'}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Lieu: ${complaint.location_incident}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Date incident: ${this.formatDate(complaint.date_incident)}`, margin, yPosition)
    yPosition += 15

    // Complainant Information
    checkNewPage(40)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS DU PLAIGNANT', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Nom: ${complaint.complainant_name}`, margin, yPosition)
    yPosition += 6
    if (complaint.complainant_id_number) {
      pdf.text(`ID: ${complaint.complainant_id_number}`, margin, yPosition)
      yPosition += 6
    }
    if (complaint.complainant_phone) {
      pdf.text(`Téléphone: ${complaint.complainant_phone}`, margin, yPosition)
      yPosition += 6
    }
    if (complaint.complainant_address) {
      pdf.text(`Adresse: ${complaint.complainant_address}`, margin, yPosition)
      yPosition += 6
    }
    yPosition += 10

    // Accused Information (Always show section)
    checkNewPage(25)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS SUR LE MIS EN CAUSE', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    if ((complaint as any).accused_name) {
      pdf.text(`Nom: ${(complaint as any).accused_name}`, margin, yPosition)
      yPosition += 6
      pdf.text('Informations supplémentaires: [À compléter lors de l\'enquête]', margin, yPosition)
    } else {
      pdf.text('Nom: [Non spécifié / À déterminer]', margin, yPosition)
      yPosition += 6
      pdf.setFont('helvetica', 'italic')
      pdf.text('Les informations seront ajoutées lors de l\'avancement de l\'enquête.', margin, yPosition)
      pdf.setFont('helvetica', 'normal')
    }
    yPosition += 15

    // Description
    if (complaint.description_fr || complaint.description_ar) {
      checkNewPage(30)
      pdf.setFont('helvetica', 'bold')
      pdf.text('DESCRIPTION DE L\'INCIDENT', margin, yPosition)
      yPosition += 10

      pdf.setFont('helvetica', 'normal')

      if (complaint.description_fr) {
        pdf.text('Description (Français):', margin, yPosition)
        yPosition += 6
        const frenchLines = pdf.splitTextToSize(complaint.description_fr, pageWidth - 2 * margin)
        frenchLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }

      if (complaint.description_ar) {
        checkNewPage(15)
        pdf.text('Description (العربية):', margin, yPosition)
        yPosition += 6
        const arabicLines = pdf.splitTextToSize(complaint.description_ar, pageWidth - 2 * margin)
        arabicLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }
    }

    // Additional Notes
    if (complaint.evidence_notes || complaint.officer_notes) {
      checkNewPage(20)
      pdf.setFont('helvetica', 'bold')
      pdf.text('NOTES SUPPLÉMENTAIRES', margin, yPosition)
      yPosition += 10

      pdf.setFont('helvetica', 'normal')

      if (complaint.evidence_notes) {
        pdf.text('Notes sur les preuves:', margin, yPosition)
        yPosition += 6
        const evidenceLines = pdf.splitTextToSize(complaint.evidence_notes, pageWidth - 2 * margin)
        evidenceLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }

      if (complaint.officer_notes) {
        checkNewPage(15)
        pdf.text('Notes de l\'officier:', margin, yPosition)
        yPosition += 6
        const officerLines = pdf.splitTextToSize(complaint.officer_notes, pageWidth - 2 * margin)
        officerLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }
    }

    // Official Information
    checkNewPage(25)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS OFFICIELLES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Officier responsable: ${complaint.officer_in_charge}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Date de création: ${this.formatDate((complaint.created_at || complaint.date_registered) || '')}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Dernière modification: ${this.formatDate((complaint.updated_at || complaint.date_registered) || '')}`, margin, yPosition)
    yPosition += 20

    // Signature and Stamp Section
    checkNewPage(60)
    pdf.setFont('helvetica', 'bold')
    pdf.text('VALIDATION OFFICIELLE', margin, yPosition)
    yPosition += 15

    // Signature area
    pdf.setFont('helvetica', 'normal')
    pdf.text('Le Commissaire (Signature):', margin, yPosition)
    yPosition += 10

    // Signature box
    pdf.setLineWidth(0.5)
    pdf.rect(margin, yPosition, 80, 30) // Signature box
    yPosition += 40

    // Official stamp area
    pdf.setFont('helvetica', 'bold')
    pdf.text('CACHET OFFICIEL:', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 10

    // Stamp circle
    const stampX = pageWidth / 2
    const stampY = yPosition + 20
    pdf.circle(stampX, stampY, 25, 'S') // Official stamp circle
    pdf.setFontSize(8)
    pdf.setFont('helvetica', 'normal')
    pdf.text('CACHET OFFICIEL', stampX, stampY - 5, { align: 'center' })
    pdf.text('POLICE NATIONALE', stampX, stampY, { align: 'center' })
    pdf.text('DU TCHAD', stampX, stampY + 5, { align: 'center' })

    // Add page numbers to all pages
    const totalPages = pdf.internal.getNumberOfPages()
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i)
      pdf.setFontSize(8)
      pdf.setFont('helvetica', 'normal')
      pdf.text(`Page ${i} sur ${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' })
    }

    // Save the PDF
    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Add text-based header for fallback PDF
   */
  private addTextBasedHeader(pdf: jsPDF, pageWidth: number, margin: number): void {
    pdf.setFontSize(12)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RÉPUBLIQUE DU TCHAD', pageWidth / 2, margin + 5, { align: 'center' })
    pdf.setFontSize(10)
    pdf.text('Présidence de la République', pageWidth / 2, margin + 12, { align: 'center' })
    pdf.text('Ministère de la Sécurité Publique et de l\'Immigration', pageWidth / 2, margin + 18, { align: 'center' })
    pdf.text('Direction Générale de la Police Nationale', pageWidth / 2, margin + 24, { align: 'center' })
    pdf.text('Direction de la Sécurité Publique', pageWidth / 2, margin + 30, { align: 'center' })

    // Line under header
    pdf.setLineWidth(1)
    pdf.line(margin, margin + 40, pageWidth - margin, margin + 40)
  }

  /**
   * Create HTML content with proper Arabic font styling (legacy method)
   */
  // Legacy methods removed to clean up codebase

  /**
   * Fallback PDF generation using text-only approach - removed for cleanup
   */

  /**
   * Create HTML content for summary report - removed for cleanup
   */

  /**
   * Generate a summary report of multiple complaints with proper pagination
   */
  public async generateSummaryReport(complaints: Complaint[], title: string = 'RAPPORT RÉCAPITULATIF'): Promise<void> {
    try {
      // Use text-based approach for summary reports (more reliable for large datasets)
      await this.generateTextBasedSummaryPDF(complaints, title)
    } catch (error) {
      console.error('Error generating summary PDF:', error)
      // Fallback to simple summary
      await this.generateFallbackSummaryPDF(complaints, title)
    }
  }

  /**
   * Generate text-based summary PDF with proper pagination
   */
  private async generateTextBasedSummaryPDF(complaints: Complaint[], title: string): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    const maxY = pageHeight - margin - 20
    let yPosition = margin

    // Helper function for new page
    const checkNewPage = (requiredSpace: number = 10): void => {
      if (yPosition + requiredSpace > maxY) {
        pdf.addPage()
        yPosition = margin + 20
        // Add header on new pages
        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'bold')
        pdf.text('RÉPUBLIQUE DU TCHAD - POLICE NATIONALE', pageWidth / 2, margin + 10, { align: 'center' })
        pdf.text(title, pageWidth / 2, margin + 16, { align: 'center' })
        yPosition += 25
      }
    }

    // Header
    this.addTextBasedHeader(pdf, pageWidth, margin)
    yPosition += 50

    // Title
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text(title, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Report info
    const reportDate = this.formatDate(new Date().toISOString())
    pdf.setFontSize(10)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Date du rapport: ${reportDate}`, margin, yPosition)
    pdf.text(`Nombre de plaintes: ${complaints.length}`, pageWidth - margin, yPosition, { align: 'right' })
    yPosition += 15

    // Statistics
    const stats = this.calculateStatistics(complaints)
    checkNewPage(40)

    pdf.setFont('helvetica', 'bold')
    pdf.text('STATISTIQUES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Total: ${stats.total}`, margin, yPosition)
    pdf.text(`En attente: ${stats.pending}`, margin + 40, yPosition)
    pdf.text(`En investigation: ${stats.investigating}`, margin + 80, yPosition)
    yPosition += 6
    pdf.text(`Résolues: ${stats.resolved}`, margin, yPosition)
    pdf.text(`Fermées: ${stats.closed}`, margin + 40, yPosition)
    yPosition += 15

    // Complaints list
    checkNewPage(30)
    pdf.setFont('helvetica', 'bold')
    pdf.text('LISTE DES PLAINTES', margin, yPosition)
    yPosition += 10

    // Table header
    pdf.setFontSize(8)
    pdf.setFont('helvetica', 'bold')
    pdf.text('Numéro', margin, yPosition)
    pdf.text('Plaignant', margin + 30, yPosition)
    pdf.text('Type', margin + 70, yPosition)
    pdf.text('Statut', margin + 110, yPosition)
    pdf.text('Date', margin + 140, yPosition)
    pdf.text('Officier', margin + 170, yPosition)
    yPosition += 6

    // Line under header
    pdf.setLineWidth(0.3)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 4

    // Complaints data
    pdf.setFont('helvetica', 'normal')
    complaints.forEach((complaint, index) => {
      checkNewPage(8)

      const statusText = this.getStatusText(complaint.status).split(' / ')[0]
      const typeText = ((complaint as any).type_name_fr || '').substring(0, 15)
      const complainantName = complaint.complainant_name.substring(0, 15)
      const officerName = complaint.officer_in_charge.substring(0, 12)
      const dateText = this.formatDate(complaint.date_registered || '').split(' ')[0]

      pdf.text(complaint.complaint_number, margin, yPosition)
      pdf.text(complainantName, margin + 30, yPosition)
      pdf.text(typeText, margin + 70, yPosition)
      pdf.text(statusText, margin + 110, yPosition)
      pdf.text(dateText, margin + 140, yPosition)
      pdf.text(officerName, margin + 170, yPosition)
      yPosition += 5

      // Add separator line every 5 rows
      if ((index + 1) % 5 === 0) {
        pdf.setLineWidth(0.1)
        pdf.line(margin, yPosition, pageWidth - margin, yPosition)
        yPosition += 2
      }
    })

    // Add page numbers
    const totalPages = pdf.internal.getNumberOfPages()
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i)
      pdf.setFontSize(8)
      pdf.setFont('helvetica', 'normal')
      pdf.text(`Page ${i} sur ${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' })
    }

    // Save the PDF
    pdf.save(`Rapport_Recapitulatif_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  /**
   * Fallback summary PDF generation
   */
  private async generateFallbackSummaryPDF(complaints: Complaint[], title: string): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const margin = 15
    let yPosition = margin

    pdf.setFont('helvetica', 'bold')
    pdf.setFontSize(16)
    pdf.text(title, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    const stats = this.calculateStatistics(complaints)
    pdf.setFont('helvetica', 'normal')
    pdf.setFontSize(12)
    pdf.text(`Total: ${stats.total}, En Attente: ${stats.pending}, Résolues: ${stats.resolved}`, margin, yPosition)
    yPosition += 15

    // Simple list of complaints
    complaints.slice(0, 30).forEach(complaint => {
      if (yPosition > 250) {
        pdf.addPage()
        yPosition = margin
      }
      pdf.text(`${complaint.complaint_number} - ${complaint.complainant_name}`, margin, yPosition)
      yPosition += 6
    })

    pdf.save(`Rapport_Recapitulatif_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  // Old text-based PDF methods removed - now using HTML-to-canvas approach for proper Arabic rendering

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  private getStatusText(status: string): string {
    const statusMap = {
      pending: 'En Attente / في الانتظار',
      investigating: 'En Investigation / قيد التحقيق',
      resolved: 'Résolue / محلولة',
      closed: 'Fermée / مغلقة'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  // getPriorityText method removed - not currently used

  private calculateStatistics(complaints: Complaint[]) {
    return {
      total: complaints.length,
      pending: complaints.filter(c => c.status === 'pending').length,
      investigating: complaints.filter(c => c.status === 'investigating').length,
      resolved: complaints.filter(c => c.status === 'resolved').length,
      closed: complaints.filter(c => c.status === 'closed').length
    }
  }
}

export const pdfGenerator = PDFGenerator.getInstance()
