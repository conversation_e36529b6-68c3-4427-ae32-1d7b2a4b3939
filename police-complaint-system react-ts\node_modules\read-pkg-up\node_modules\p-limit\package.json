{"name": "p-limit", "version": "1.3.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "license": "MIT", "repository": "sindresorhus/p-limit", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "dependencies": {"p-try": "^1.0.0"}, "devDependencies": {"ava": "*", "delay": "^2.0.0", "in-range": "^1.0.0", "random-int": "^1.0.0", "time-span": "^2.0.0", "xo": "*"}}